# frozen_string_literal: true

require 'rails_helper'

describe 'List incidents' do
  let(:admin_user) { create :admin_user, :admin }

  before { login_as(admin_user) }

  it 'shows all columns', :aggregate_failures do # rubocop:disable RSpec/ExampleLength
    visit '/incidents'

    within 'form' do
      expect(page).to have_field('length')
      expect(page).to have_field('search[value]')
    end

    within 'thead' do
      expect(page).to have_content('Data')
      expect(page).to have_content('<PERSON><PERSON><PERSON>')
      expect(page).to have_content('Profissional')
      expect(page).to have_content('Aviso Recebido')
      expect(page).to have_content('Tipo')
      expect(page).to have_content('Criticidade')
      expect(page).to have_content('Ação')
    end
  end

  it 'returns empty result without search value' do
    visit '/incidents'

    expect(page).to have_content('Nenhum resultado encontrado')
  end

  it 'returns result for a search value', :aggregate_failures do
    create :incident, :cancel, tasker: create(:tasker, name: '<PERSON>')
    create :incident, :delayed

    visit '/incidents'
    fill_in 'search[value]', with: "john\n"

    expect(page).to have_content('<PERSON> Due')
  end

  it 'returns ordered result by incident_at', :aggregate_failures do # rubocop:disable RSpec/ExampleLength
    create :incident, :no_show, incident_at: Time.zone.parse('2025/05/12 10:00')
    create :incident, :no_show, incident_at: Time.zone.parse('2025/05/12 08:00')

    visit '/incidents'
    fill_in 'search[value]', with: "falta\n"
    click_on 'Data' # asc

    within 'tbody' do
      expect(page).to have_content('12/05/2025 08h00')
      expect(page).to have_content('12/05/2025 10h00')
    end

    click_on 'Data' # desc
    within 'tbody' do
      expect(page).to have_content('12/05/2025 10h00')
      expect(page).to have_content('12/05/2025 08h00')
    end
  end
end
