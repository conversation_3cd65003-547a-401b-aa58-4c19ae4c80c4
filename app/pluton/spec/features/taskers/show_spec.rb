# frozen_string_literal: true

require 'rails_helper'
require 'helpers/boolean'

describe 'Show tasker' do
  let(:background_check_status) { 'unchecked' }
  let(:background_check_last_result) { nil }
  let(:background_check_last_score) { nil }
  let(:background_checked_at) { nil }
  let(:bring_products) { create :tasker_flag }
  let(:receive_preferential_offers) { create :tasker_flag, :receive_preferential_offers }
  let(:background_check) { instance_double(Workers::BackgroundCheck::AddTaskers, run: true) }
  let(:chat_tasker_creator) { instance_double(Workers::Chat::TaskerCreator, publish_user: true) }
  let(:mei) { CNPJ.generate(true) }
  let(:mei_password) { 'mei-passphrase' }

  let(:tasker) do
    create :tasker, history: build(:tasker_history, background_check_status: background_check_status,
                                                    background_check_last_result: background_check_last_result,
                                                    background_check_last_score: background_check_last_score,
                                                    background_checked_at:),
                    flags: [bring_products, receive_preferential_offers], mei:, mei_password:
  end

  let(:admin_user) { create :admin_user, :admin }

  before do
    login_as(admin_user)
    visit "/taskers/#{tasker.id}"
  end

  it { expect(page).to have_link('Abrir página do profissional', href: /profissionais.parafuzo.com/) }
  it { expect(page).to have_button('Verificar Antecedentes') }
  it { expect(page).to have_content('Antecedentes Criminais') }
  it { expect(page).to have_content('Verificação MEI') }
  it { expect(page).to have_content('Ativo') }
  it { expect(page).to have_content('Situação') }
  it { expect(page).not_to have_content('Resultado') }
  it { expect(page).not_to have_content('Último score obtido') }
  it { expect(page).not_to have_content('Data da verificação') }
  it { expect(page).to have_content('Integrações') }
  it { expect(page).to have_content('Acordos') }

  it { expect(page).to have_content(/Levar produtos.*Quer.*Pode/m) }
  it { expect(page).to have_content(/Receber ofertas de preferencial.*Quer.*Pode/m) }

  it {
    expect(page).to have_content(
      "Categoria\nSem categoria " \
      '(Pontos de fidelização: 0 / Jobs completados: 0 / Índice de fidelização: 0%)'
    )
  }

  shared_examples_for 'shows background check content' do |score|
    let(:background_check_last_score) { score }
    let(:background_checked_at) { Time.current.utc }

    it { expect(page).to have_content('Verificado') }
    it { expect(page).to have_content('Resultado') }
    it { expect(page).to have_content('Último score obtido') }
    it { expect(page).to have_content('Data da verificação') }
  end

  shared_examples_for 'shows mei check content' do |message, active|
    before do
      login_as(admin_user)
      visit "/taskers/#{tasker.id}"
    end

    it { expect(page).to have_text "Situação\nVerificado" }
    it { expect(page).to have_text "Resultado\n#{message}" }
    it { expect(page).to have_text "Ativo\n#{active}" }
    it { expect(page).to have_content('Data da verificação') }
  end

  shared_examples_for 'hides mei check content' do |state|
    before do
      login_as(admin_user)
      visit "/taskers/#{tasker.id}"
    end

    it { expect(page).to have_text "Situação\n#{state}" }
    it { expect(page).to have_text "Ativo\nNão" }
    it { expect(page).to have_no_content('Resultado') }
    it { expect(page).to have_no_content('Data da verificação') }
  end

  context 'when we click on send tasker button to the background check' do
    before do
      allow(Parafuzo::Core::Queue).to receive(:publish)
      click_button('Verificar Antecedentes')
    end

    it { expect(page).to have_content('Verificando') }
    it { expect(page).to have_content('Prestador enviado para verificação dos antecedentes criminais.') }
  end

  context 'when click mei check button' do
    before do
      allow(Parafuzo::Core::Queue).to receive(:publish)
      click_link_or_button('Verificar MEI')
    end

    it { expect(page).to have_content('Verificando') }
    it { expect(page).to have_content('Prestador enviado para verificação do MEI.') }
  end

  context 'when the tasker is being verified' do
    let(:background_check_status) { 'checking' }

    it { expect(page).to have_content('Verificando') }
  end

  context 'when the tasker was verified, but not approved' do
    let(:background_check_status) { 'checked' }
    let(:background_check_last_result) { 'not approved' }

    it_behaves_like 'shows background check content', 1400

    it { expect(page).to have_content('Não aprovado') }
  end

  context 'when the tasker was verified and approved' do
    let(:background_check_status) { 'checked' }
    let(:background_check_last_result) { 'approved' }

    it_behaves_like 'shows background check content', 90

    it { expect(page).to have_content('Aprovado') }
  end

  context 'when the tasker was verified, but his CPF number is invalid' do
    let(:background_check_status) { 'checked' }
    let(:background_check_last_result) { 'invalid cpf' }

    it_behaves_like 'shows background check content', 0

    it { expect(page).to have_content('CPF inválido') }
  end

  context 'when the tasker was verified, but an error occurred' do
    let(:background_check_status) { 'checked' }
    let(:background_check_last_result) { 'an error has ocurred' }

    it_behaves_like 'shows background check content', 0

    it { expect(page).to have_content('Não foi possível verificar') }
  end

  context 'when the tasker company is being verified' do
    before { create(:tasker_company, tasker:, state: 'checking') }

    it_behaves_like 'hides mei check content', 'Verificando'
  end

  context 'when the tasker company was not verified' do
    before { create(:tasker_company, tasker:, state: 'unchecked') }

    it_behaves_like 'hides mei check content', 'Não verificado'
  end

  context 'when the tasker company has been verified and is active' do
    before { create :tasker_company, :active, tasker: }

    it_behaves_like 'shows mei check content', 'Ativo', 'Sim'
  end

  context 'when the tasker company has been verified and is write off' do
    before { create(:tasker_company, tasker:, state: 'checked', result_message: 'baixada', checked_at: Time.current) }

    it_behaves_like 'shows mei check content', 'Baixado', 'Não'
  end

  context 'when the tasker company has been verified and is null' do
    before { create(:tasker_company, tasker:, state: 'checked', result_message: 'nula', checked_at: Time.current) }

    it_behaves_like 'shows mei check content', 'Nulo', 'Não'
  end

  context 'when the tasker company has been verified and is suspense' do
    before { create(:tasker_company, tasker:, state: 'checked', result_message: 'suspensa', checked_at: Time.current) }

    it_behaves_like 'shows mei check content', 'Suspenso', 'Não'
  end

  context 'when the tasker company has been verified and is unfit' do
    before { create(:tasker_company, tasker:, state: 'checked', result_message: 'inapta', checked_at: Time.current) }

    it_behaves_like 'shows mei check content', 'Inapto', 'Não'
  end

  context 'when the tasker company has been verified, but the CNPJ doest not exist' do
    before do
      create(:tasker_company, tasker:, state: 'checked',
                              result_message: 'cnpj does not exist in receita federal database',
                              checked_at: Time.current)
    end

    it_behaves_like 'shows mei check content', 'CNPJ não existe na Receita Federal', 'Não'
  end

  context 'when the tasker has not the mei number filled' do
    let(:mei) { '' }

    before do
      login_as(admin_user)
      visit "/taskers/#{tasker.id}"
    end

    it { expect(page).to have_no_content('Verificação MEI') }
    it { expect(page).to have_no_button('Verificar MEI') }
  end

  context 'when the tasker has the mei number filled' do
    let(:mei) { '08.127.821/0001-03' }

    before do
      login_as(admin_user)
      visit "/taskers/#{tasker.id}"
    end

    it { expect(page).to have_button('Verificar MEI') }
  end

  context 'when the tasker has not the mei password filled' do
    let(:mei_password) { '' }

    before do
      login_as(admin_user)
      visit "/taskers/#{tasker.id}"
    end

    it { expect(page).to have_no_content('Verificação MEI') }
  end

  context 'when the tasker has sendbird integration' do
    let(:tasker) { create :tasker, integrations: { sendbird: { id: 'tasker:some_id' } } }

    before { allow(Workers::BackgroundCheck::AddTaskers).to receive(:new).and_return(background_check) }

    it { expect(page).to have_content('Chat') }
  end

  context 'when click on the password reset button' do
    let(:tasker_service) { instance_double TaskerService }
    let(:mailer) { instance_double Resque::Mailer::MessageDecoy, deliver: true }

    before do
      allow(TaskerService).to receive(:new).with(tasker).and_return tasker_service
      allow(tasker_service).to receive(:reset_password!).and_return mailer

      click_button 'Resetar senha do aplicativo'
    end

    it { expect(page).to have_content 'Um email com a nova senha foi enviado para o profissional.' }
  end

  context 'when admin_user has access to update tasker' do
    before do
      login_as(create(:admin_user, role: :user))

      visit "/taskers/#{tasker.id}"
    end

    it { expect(page).to have_button('Resetar senha do aplicativo') }
  end

  context 'when admin_user does not have access to update tasker' do
    before do
      login_as(create(:admin_user, role: :sales))

      visit "/taskers/#{tasker.id}"
    end

    it { expect(page).not_to have_button('Resetar senha do aplicativo') }
  end

  context 'when tasker is lead' do
    let(:tasker) { create :tasker, state: 'lead' }

    it { expect(page).to have_text "Situação\nLead" }
    it { expect(page).to have_button('Ativar') }
    it { expect(page).to have_no_button('Desativar Permanentemente') }
  end

  context 'when tasker is onboarding' do
    let(:tasker) { create :tasker, state: 'onboarding' }

    it { expect(page).to have_text "Situação\nOnboarding" }
    it { expect(page).to have_button('Desativar Permanentemente') }
    it { expect(page).to have_no_button('Ativar') }
  end

  context 'when tasker is suspended' do
    let(:tasker) { create :suspended_tasker }

    before do
      allow(Workers::BackgroundCheck::AddTaskers).to receive(:new).and_return(background_check)
      allow(Workers::Chat::TaskerCreator).to receive(:new).and_return(chat_tasker_creator)
    end

    it { expect(page).to have_text "Situação\nSuspenso" }
    it { expect(page).to have_button('Ativar') }
    it { expect(page).to have_no_button('Desativar Permanentemente') }

    it 'changes the tasker state from suspended to onboarding' do
      expect { click_link_or_button 'Ativar' }.to change { tasker.reload.state }.from('suspended').to('onboarding')
    end
  end

  context 'when tasker is disabled' do
    let(:tasker) { create :tasker, state: 'disabled' }

    before do
      allow(Workers::BackgroundCheck::AddTaskers).to receive(:new).and_return(background_check)
      allow(Workers::Chat::TaskerCreator).to receive(:new).and_return(chat_tasker_creator)
    end

    it { expect(page).to have_text "Situação\nInativo" }
    it { expect(page).to have_button('Ativar') }
    it { expect(page).to have_no_button('Desativar Permanentemente') }

    it 'changes the tasker state from disabled to onboarding' do
      expect { click_link_or_button 'Ativar' }.to change { tasker.reload.state }.from('disabled').to('onboarding')
    end
  end

  context 'when tasker is enabled' do
    let(:tasker) { create :tasker, state: 'enabled' }

    before { allow(Workers::BackgroundCheck::AddTaskers).to receive(:new).and_return(background_check) }

    it { expect(page).to have_text "Situação\nAtivo" }
    it { expect(page).to have_button('Desativar Permanentemente') }
    it { expect(page).to have_no_button('Ativar') }

    it 'deactivates the tasker' do
      expect { click_link_or_button 'Desativar Permanentemente' }.to change {
        tasker.reload.state
      }.from('enabled').to('disabled')
    end
  end

  describe 'history_jobs' do
    let(:tasker) { create :tasker }

    it { expect(page).to have_link 'Histórico de fidelização' }
    it { expect(page).to have_content "Histórico de fidelização\nCarregando..." }
  end

  describe 'processed_feedbacks' do
    let(:tasker) { create :tasker }

    it { expect(page).to have_content 'Feedbacks processados' }
    it { expect(page).to have_content 'Comentário' }
    it { expect(page).to have_content 'Serviço' }
    it { expect(page).to have_content 'Estado' }
    it { expect(page).to have_content 'Criado em' }
    it { expect(page).to have_content 'Ação' }
  end

  describe 'devices' do
    it { expect(page).to have_content('Dispositivos') }
    it { expect(page).to have_content('Identificador') }
    it { expect(page).to have_content('Versão do app') }
    it { expect(page).to have_content('Último login') }
    it { expect(page).to have_content('Último logout') }
  end

  context 'when the user is not an admin or owner' do
    let(:tasker) { create :tasker, state: 'enabled' }
    let(:non_privileged_user) { create :admin_user, role: :user }

    before do
      login_as(non_privileged_user)
      visit "/taskers/#{tasker.id}"
    end

    it { expect(page).to have_text "Situação\nAtivo" }
    it { expect(page).to have_no_button('Ativar') }
    it { expect(page).to have_no_button('Desativar Permanentemente') }
  end
end
