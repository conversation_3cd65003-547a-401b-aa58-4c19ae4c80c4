<%= render partial: 'shared/content_header', locals: { section_title: 'Prestadores', page_title: (@tasker.name || "Exibir") } %>

<% content_for :title, @tasker.name %>

<section class="panel">
  <%= panel_header(collapsable: true, collapsed: false, target: 'taskerInfo') do %>
    <div class="widget-profile is-flex">
      <div class="profile-picture col-md-3 text-right">
        <%= image_tag @tasker.photo.url('desktop') %>
      </div>

      <div class="profile-info col-md-9">
        <div>
          <h4 class="panel-title text-semibold mb-xs">
            <%= render 'shared/text_with_copy', text: @tasker.name || 'Prestador' %>

            <span class="label label-danger ml-xs">
              <%= t(@tasker.plan, scope: [:tasker, :plan]) %>
            </span>
          </h4>
        </div>

        <% if @tasker.blumpa_id? %>
          <%= link_to blumpa_url("profissionais/#{@tasker.blumpa_id}"), class: 'btn btn-success mt-xs mb-xs', target: :blank, data: { 'prevent-collapse' => true } do %>
            Abrir no Blumpa
          <% end %>
        <% end %>

        <%= link_to tasker_profile_url(@tasker), class: 'btn btn-success mt-xs mb-xs', target: :blank, data: { 'prevent-collapse' => true } do %>
          Abrir página do profissional
        <% end %>

        <% if @tasker.cpf? %>
          <p class="text-dark">
            <i class="fa fa-fw fa-list-alt va-middle text-muted text-md"></i>
            <%= render 'shared/text_with_copy', text: @tasker.cpf %>
          </p>

        <% end %>

        <% if @tasker.rg? %>
          <p class="text-dark">
            <i class="fa fa-fw fa-list-alt va-middle text-muted text-md"></i>
            <%= render 'shared/text_with_copy', text: @tasker.rg %>
          </p>
        <% end %>

        <p class="text-dark">
          <i class="fa fa-fw fa-map-marker va-middle text-muted text-md"></i>
          <%= render 'shared/text_with_copy', text: @tasker.interest_address %>
        </p>

        <% @tasker.phones.each do |phone| %>
          <p class="text-dark">
            <i class="fa fa-fw fa-phone va-middle text-muted text-md"></i>
            <%= render 'shared/text_with_copy', text: phone.number %>
          </p>
        <% end if can? :read_full, Tasker %>

        <p class="text-dark">
          <i class="fa fa-fw fa-mobile va-middle text-muted text-md"></i>
          Última versão do app
          <% if @tasker.notify_app_version %>
            <span class="label label-info"><%= @tasker.notify_app_version %></span>
          <% else %>
            <span class="label label-warning">não identificado</span>
          <% end %>
        </p>
      </div>
    </div>
  <% end %>

  <%= collapse_wrapper(collapsed: false, target: 'taskerInfo') do %>
    <div class="panel-body">
      <div class="form-horizontal">
        <div class="form-group mb-none">
          <label class="col-md-3 control-label">Situação</label>

          <div class="col-md-9 form-control-static">
            <%= content_tag :label,
                            @tasker.translated_state,
                            class: "label #{@tasker.active? ? 'label-success' : 'label-default'}" %>

            <% if can? :read_full, Tasker %>
              <% if !@tasker.active? %>
                <%= button_to 'Ativar', activate_tasker_path(@tasker), method: :put, class: 'btn btn-success btn-sm' if can? :activate, Tasker %>
              <% else %>
                <%= button_to 'Desativar Permanentemente', deactivate_tasker_path(@tasker), method: :put, class: 'btn btn-danger btn-sm', data: { confirm: t('tasker.confirm_deactivate') } if can? :manage, Tasker %>
              <% end %>
            <% end %>
          </div>

          <div class="col-md-12 form-control-static">
            <%= render partial: 'components/integration_tracker_list', locals: { model: @tasker } %>
          </div>

          <label class="col-md-3 control-label">Categoria</label>

          <div class="col-md-9 form-control-static">
            <span class="label label-<%= @tasker.category == 'uncategorized' ? 'default' : @tasker.category %>">
              <%= t(@tasker.category, scope: 'scores.category') %>
            </span>
            <span>
              (Pontos de fidelização: <%= @tasker.history.partial_fidelity_points %> /
              Jobs completados: <%= @tasker.history.partial_completed_jobs %> /
              Índice de fidelização: <%= @tasker.fidelity_percentage %>)
            </span>
          </div>
          <% if can? :read_full, Tasker %>
            <%= render 'scores', tasker: @tasker %>

          <label class="col-md-3 control-label"> Notificação por</label>
          <div class="col-md-9 form-control-static">
            <span class="label label-info"><%= @tasker.notify_type %></span>
          </div>
        </div>

        <hr />

        <%= render 'components/label_and_value', label: 'Pagamento Automático', value: bool_label(@tasker.accepts_payout) %>

        <%= render 'components/label_and_value', label: 'Aceita pagamento em dinheiro?' , value: bool_label(@tasker.payable_by_cash) %>

        <hr />

        <% if @tasker.banks.any? %>
          <div class="form-group mb-none">
            <label class="col-md-3 control-label">Contas Bancárias</label>

            <div class="col-md-9 form-control-static">
              <%= render 'shared/banks_show', banks: @tasker.banks %>
            </div>
          </div>
        <% else %>
          <%= render 'components/label_and_value', label: 'Conta bancária', value: "Nenhuma cadastrada" %>
        <% end %>

        <div class="form-group">

          <div class="col-md-offset-3 col-md-6">
            <div class="row">
              <div class="col-sm-12 form-control-static">
                <div class="btn-toolbar">
                  <a href="<%= tasker_statement_path(@tasker) %>" class="btn btn-default btn-sm">Ver Extrato</a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <hr>
        <div class="form-group mb-none">
          <label class="col-md-3 control-label">Antecedentes Criminais</label>

          <div class="col-md-8">
            <div class="row form-control-static">
              <div class="col-sm-6">
                <%= render 'components/label_and_value', label: 'Situação', value: background_check_label(@tasker.history.background_check_status), class_label: 'col-md-8', class_value: 'col-md-4' %>

                <% if !@tasker.history.background_check_last_result.blank? %>
                  <%= render 'components/label_and_value', label: 'Resultado', value: background_check_label(@tasker.history.background_check_last_result), class_label: 'col-md-8', class_value: 'col-md-4' %>
                <% end %>
              </div>

              <% if @tasker.history.background_checked_at.present? && @tasker.history.background_check_last_score.present?  %>
                <div class="col-sm-6">
                  <%= render 'components/label_and_value', label: 'Último score obtido', value: @tasker.history.background_check_last_score, class_label: 'col-md-8', class_value: 'col-md-4' %>
                  <%= render 'components/label_and_value', label: 'Data da verificação', value: l(@tasker.history.background_checked_at), class_label: 'col-md-8', class_value: 'col-md-4' %>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <% if @tasker.mei.present? && @tasker.mei_password.present? %>
          <hr>

          <div class="form-group mb-none">
            <label class="col-md-3 control-label">Verificação MEI</label>

            <div class="col-md-8">
              <div class="row form-control-static">
                <div class="col-sm-6">
                  <%= render 'components/label_and_value', label: 'Situação', value: background_check_label(@tasker.company.state), class_label: 'col-md-8', class_value: 'col-md-4' %>
                  <%= render 'components/label_and_value', label: 'Resultado', value: background_check_label(@tasker.company.result_message), class_label: 'col-md-8', class_value: 'col-md-4' if @tasker.company.result_message.present?  %>
                </div>

                <div class="col-sm-6">
                  <%= render 'components/label_and_value', label: 'Ativo', value: bool_label(@tasker.company.active), class_label: 'col-md-8', class_value: 'col-md-4' %>
                  <%= render 'components/label_and_value', label: 'Data da verificação', value: l(@tasker.company.checked_at), class_label: 'col-md-8', class_value: 'col-md-4' if @tasker.company.checked_at.present? %>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <hr>

        <%= render 'components/label_and_value', label: 'Serviços', value: @tasker.services.map { |service| t(service.name, scope: 'services.short_name') }.to_sentence if can? :read_full, Tasker %>

        <hr>

        <%= render "taskers/show/preferential", tasker: @tasker %>

        <div class="form-group mb-none">
          <label class="col-md-3 control-label">MEI</label>

          <div class="col-md-8">
            <div class="row form-control-static">
              <div class="col-sm-4">
                <%= @tasker.mei %>
              </div>

              <div class="col-sm-4">
                Senha: <%= @tasker.mei_password %>
              </div>
            </div>
          </div>
        </div>

        <div class="form-group mb-none">
          <label class="col-md-3 control-label">Email</label>
          <div class="col-md-8">
            <div class="row form-control-static">
              <div class="col-sm-4">
                <%= render 'shared/text_with_copy', text: @tasker.email %>
              </div>
            </div>
          </div>
        </div>

        <%= render 'components/label_and_value', label: Tasker.human_attribute_name(:gender)        , value: (@tasker.gender.blank? ? 'Não informado' : @tasker.gender_humanize) %>
        <%= render 'components/label_and_value', label: Tasker.human_attribute_name(:marital_status), value: (@tasker.marital_status.blank? ? 'Não informado' : t(@tasker.marital_status, scope: :marital_status)) %>
        <%= render 'components/label_and_value', label: Tasker.human_attribute_name(:has_children)  , value: bool_label(@tasker.has_children) %>
        <%= render 'components/label_and_value', label: Tasker.human_attribute_name(:pets_allowed)  , value: bool_label(@tasker.pets_allowed) %>

        <% @tasker.flags.each do |flag| %>
          <div class="form-group mb-none">
            <label class="col-md-3 form-label"><%= t(flag.name, scope: 'tasker.flag') %></label>
            <div class="col-md-6">
              <label><%= t(:accepted, scope: 'tasker.flag') %></label> <%= bool_label(flag.accepted) %>
              <label><%= t(:allowed, scope: 'tasker.flag') %></label> <%= bool_label(flag.allowed) %>
            </div>
          </div>
        <% end %>

        <%= render 'components/label_and_value', label: Tasker.human_attribute_name(:education)     , value: (@tasker.education.blank? ? 'Não informado' : t(@tasker.education, scope: :education) ) %>
        <%= render 'components/label_and_value', label: Tasker.human_attribute_name(:phone_brand)   , value: (@tasker.phone_brand.blank? ? 'Não informado' : t(@tasker.phone_brand, scope: :phone_brand) ) %>

        <%= render 'components/label_and_value', label: 'Regiões de Atendimento', value: @tasker.zones.map { |zone| t(zone, scope: :zones) unless zone.blank? }.compact.to_sentence if can? :read_full, Tasker %>
        <%= render 'components/label_and_value', label: 'Disponibilidade', value: @tasker.availability.map { |day| t(:abbr_day_names, scope: :date)[Tasker::DAYS.index(day.to_sym)] unless day.blank? }.compact.to_sentence if can?(:read_full, Tasker) and @tasker.availability %>
        <%= render 'components/label_and_value', label: 'Observações', value:  @tasker.notes %>
        <%= render 'components/label_and_value', label: 'Data de cadastro', value:  l(@tasker.created_at) %>

        <hr>

        <% if can? :read_full, Tasker %>
          <%= render 'components/label_and_value', label: 'Indicação', value:  @tasker.indication %>
          <%= render 'components/label_and_value', label: 'Nota da prova', value:  @tasker.test_score %>
            <hr>
            <%= render 'components/label_and_value', label: 'Endereço residencial', value:  @tasker.address.try(:inline) %>
            <div class="form-group mb-none">
              <label class="col-md-3 control-label">Localização</label>
              <div class="col-md-6">
                <%= render 'taskers/location', tasker: @tasker, current_location: @current_location %>
              </div>
            </div>

            <% end %>
        <% end %>
      </div>
    </div>

    <div class="panel-footer">
      <%= button_to 'Verificar MEI', mei_check_tasker_path(@tasker), method: :put, class: 'mb-xs mt-xs mr-xs btn btn-warning pull-right' if @tasker.mei.present? %>
      <%= button_to 'Verificar Antecedentes', background_check_tasker_path(@tasker), method: :put, class: 'mb-xs mt-xs mr-xs btn btn-success pull-right' %>
      <%= button_to t('tasker.reset_password_tasker'), reset_password_tasker_path(@tasker), method: :patch, class: 'mb-xs mt-xs mr-xs btn btn-primary pull-right', data: { confirm: t('tasker.confirm_reset') } if can? :update, Tasker %>
      <%= link_to 'Editar Prestador', edit_tasker_path(@tasker), class: 'mb-xs mt-xs mr-xs btn btn-primary pull-right' %>
      <%= link_to 'Validar', validate_tasker_path(@tasker), class: 'mb-xs mt-xs mr-xs btn btn-default pull-right' %>
      <input type="button" value="Voltar" onClick="history.go(-1);" class="mb-xs mt-xs mr-xs btn btn-info">
    </div>
  <% end %>
</section>

<% if can? :read_full, Tasker %>
  <%= render 'admin_notes/list', url: tasker_admin_notes_path(@tasker), new_url: new_tasker_admin_note_path(@tasker) %>
  <%= render 'jobs/list', hide_new_button: true, collapsable: true %>
  <%= render 'taskers/tasker_history_jobs/list' %>
  <%= render 'offers/list', lazy: true %>
  <%= render 'messages/list', lazy: true %>
  <%= render 'feedbacks/list', collapsable: true, lazy: true %>
  <%= render 'taskers/processed_feedbacks/list', url: tasker_processed_feedbacks_path(@tasker), collapsable: true, lazy: true %>
  <%= render 'incidents/list', collapsable: true, lazy: true %>
  <%= render 'deals/list', collapsable: true, lazy: true %>
  <%= render 'devices/list', devices: @tasker.devices.order_by(:last_login_at.asc) %>
  <%= render 'events/table', lazy: true %>
<% end %>
