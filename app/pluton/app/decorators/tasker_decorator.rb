# frozen_string_literal: true

class TaskerDecorator < Draper::Decorator
  include ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>

  delegate_all

  TABLE_LIST_COLUMNS = {
    name: { width: 3 },
    cpf: { width: 2 },
    city: { width: 2 },
    state: { width: 2 },
    services: { width: 3 }
  }.freeze
  COLUMNS = TABLE_LIST_COLUMNS.keys.freeze

  def score
    ("%0.02f" % object.score).to_f
  end

  def service_names
    object.services.map { |service| h.t(service.name, scope: 'services.short_name') unless service.blank? }.compact.to_sentence
  end

  def zone_names
    zone_list.map { |zone| h.t(zone, scope: :zones) unless zone.blank? }.compact.to_sentence
  end

  def phone_numbers
    object.phones.map(&:number).to_sentence
  end

  def last_job_date
    @last_job_date ||= begin
      last_job = object.last_job

      h.l(last_job.date) if last_job&.date
    end
  end

  def links
    {}.tap do |link|
      link[:edit]      = { href: h.edit_tasker_path(object) }
      link[:show]      = { href: h.tasker_path(object) }
      link[:job_route] = { href: job_route_link } if job_route_link
    end
  end

  def preferentials
    Taskers::Calendar.new(object).run
  end

  def as_json(options)
    {
      id:                 id.to_s,
      name:               name,
      service_names:      service_names,
      zone_names:         zone_names,
      active_name:        active_name,
      last_job_date:      last_job_date,
      phone_numbers:      phone_numbers,
      coordinates:        coordinates,
      # TODO (SCORE) remove this when not use more
      score:              0.0,
      scores:             scores,
      _links:             links
    }
  end

  def fidelity_percentage
    "#{number_with_precision(object.fidelity_rate * 100, precision: 0)}%"
  end

  private

  def zone_list
    object.zones || []
  end

  def job_route_link
    return unless context[:job]

    h.route_job_url(context[:job], object)
  end
end
